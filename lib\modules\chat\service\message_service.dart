import 'dart:async';
import 'dart:math' as math;
import 'package:get/get.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/enums/message_type.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/modules/chat/repository/chat_repository.dart';
import 'package:rolio/modules/chat/service/chat_manager.dart';
import 'package:synchronized/synchronized.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:rolio/common/utils/message_tracker.dart';
import 'package:rolio/manager/ws_message_manager.dart';

/// 消息状态管理器
/// 
/// 管理消息的状态、缓存和清理
class MessageStateManager {
  // 消息列表和状态
  final RxList<Message> messages = <Message>[].obs;
  final RxBool isAiReplying = false.obs;
  final RxMap<int, bool> aiRoleReplyingMap = <int, bool>{}.obs; // 添加角色回复状态Map
  final RxBool isLoadingHistory = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool hasMoreMessages = true.obs;
  final RxInt currentPage = RxInt(1);
  
  // 当前AI角色ID
  final RxInt currentAiRoleId = RxInt(0); // 添加当前角色ID
  
  // 缓存管理
  final int maxProcessedMessageIds;
  final int maxMessagesPerConversation;
  final Set<String> processedMessageIds = <String>{};
  
  // 会话ID管理
  String _currentconversationid = DateTime.now().millisecondsSinceEpoch.toString();
  String get currentconversationid => _currentconversationid;
  
  // 会话分页状态管理
  final Map<int, ConversationPagingState> conversationStates = {};
  
  // 构造函数
  MessageStateManager({
    this.maxProcessedMessageIds = 500,
    this.maxMessagesPerConversation = 100,
  }) {
    _setupCleanupTimer();
  }
  
  // 设置清理Worker - 使用Stream.periodic
  void _setupCleanupTimer() {
    // 使用Stream.periodic创建周期性清理任务
    final cleanupStream = Stream.periodic(
      const Duration(minutes: 10),
      (count) => count,
    );

    cleanupStream.listen((_) => _cleanupCache());
  }
  
  // 清理缓存
  void _cleanupCache() {
    _cleanupConversationStates();
    _cleanupProcessedMessageIds();
    _cleanupExcessiveMessages();
  }
  
  // 清理过期会话状态
  void _cleanupConversationStates() {
    final now = DateTime.now();
    final expiredKeys = <int>[];
    
    conversationStates.forEach((id, state) {
      if (now.difference(state.lastAccessed).inHours > 1) {
        expiredKeys.add(id);
      }
    });
    
    for (final key in expiredKeys) {
      conversationStates.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      LogUtil.debug('已清理${expiredKeys.length}个过期会话状态');
    }
  }
  
  // 清理过多的消息ID缓存
  void _cleanupProcessedMessageIds() {
    if (processedMessageIds.length > maxProcessedMessageIds) {
      final toRemove = processedMessageIds.length - (maxProcessedMessageIds ~/ 2);
      final iterator = processedMessageIds.iterator;
      int count = 0;
      
      final idsToRemove = <String>[];
      while (iterator.moveNext() && count < toRemove) {
        idsToRemove.add(iterator.current);
        count++;
      }
      
      for (final id in idsToRemove) {
        processedMessageIds.remove(id);
      }
      
      LogUtil.debug('已清理$count个过期消息ID缓存');
    }
  }
  
  // 清理过多的消息
  void _cleanupExcessiveMessages() {
    if (messages.length > maxMessagesPerConversation) {
      // 保留最新的消息，清理旧消息
      // 由于消息现在按插入顺序排列，我们从列表开头移除旧消息
      final excessMessages = messages.length - maxMessagesPerConversation;
      
      if (excessMessages > 0) {
        messages.removeRange(0, excessMessages);
        LogUtil.debug('清理了$excessMessages条旧消息，当前消息数量: ${messages.length}');
      }
    }
  }
  
  // 获取会话分页状态
  ConversationPagingState getConversationState(int conversationId) {
    if (!conversationStates.containsKey(conversationId)) {
      conversationStates[conversationId] = ConversationPagingState();
    }
    
    // 更新最后访问时间
    conversationStates[conversationId]!.lastAccessed = DateTime.now();
    
    return conversationStates[conversationId]!;
  }
  
  // 设置当前会话ID
  void setCurrentConversationId(String conversationid) {
    _currentconversationid = conversationid;
  }
  
  // 设置当前AI角色ID
  void setCurrentAiRoleId(int roleId) {
    currentAiRoleId.value = roleId;
  }
  
  // 设置AI角色的回复状态
  void setAiRoleReplyingStatus(int roleId, bool isReplying) {
    if(roleId > 0) {
      aiRoleReplyingMap[roleId] = isReplying;
      aiRoleReplyingMap.refresh();
    }
  }
  
  // 获取AI角色的回复状态
  bool isAiRoleReplying(int roleId) {
    return aiRoleReplyingMap[roleId] ?? false;
  }
  
  // 当设置全局AI回复状态时，同时更新当前角色的状态
  void updateAiReplyingStatus(bool isReplying) {
    isAiReplying.value = isReplying;
    if(currentAiRoleId.value > 0) {
      setAiRoleReplyingStatus(currentAiRoleId.value, isReplying);
    }
  }
  
  // 重置状态
  void reset() {
    messages.clear();
    isAiReplying.value = false;
    isLoadingHistory.value = false;
    isLoadingMore.value = false;
    currentPage.value = 1;
    
    // 生成新的会话ID
    _currentconversationid = DateTime.now().millisecondsSinceEpoch.toString();
    LogUtil.debug('已重置会话状态，新会话ID: $_currentconversationid');
  }
  
  // 切换会话ID
  void switchSession(String newconversationid) {
    _currentconversationid = newconversationid;
    LogUtil.debug('已切换到新会话ID: $_currentconversationid');
  }
  
  // 生成新的会话ID
  String generateNewconversationid() {
    _currentconversationid = DateTime.now().millisecondsSinceEpoch.toString();
    return _currentconversationid;
  }
  
  // 清除特定会话状态
  void clearConversationState(int conversationId) {
    conversationStates.remove(conversationId);
  }
  
  // 清除所有会话状态
  void clearAllConversationStates() {
    conversationStates.clear();
  }
}

/// 会话分页状态类
class ConversationPagingState {
  int currentPage = 1;
  bool hasMoreMessages = true;
  DateTime lastAccessed = DateTime.now();
  
  ConversationPagingState({this.currentPage = 1, this.hasMoreMessages = true});
  
  void reset() {
    currentPage = 1;
    hasMoreMessages = true;
    lastAccessed = DateTime.now();
  }
  
  void updateLastAccessed() {
    lastAccessed = DateTime.now();
  }
}

/// 消息服务
///
/// 负责消息处理、发送和接收、历史消息加载
class MessageService extends GetxService {
  // 依赖服务
  final GlobalState _globalState;
  final WsManager _wsManager = Get.find<WsManager>();
  final ChatRepository _repository;
  final ChatManager _chatManager;
  final ISessionProvider? _sessionProvider;
  // EventBus已迁移到GetX响应式机制，不再需要
  
  // 状态管理器
  final MessageStateManager _stateManager;
  
  // 消息同步机制
  final Lock _messagesLock = Lock();
  
  // UUID生成器
  final Uuid _uuid = Uuid();
  
  // 消息跟踪器
  final MessageTracker _messageTracker = MessageTracker();

  // 发送缓冲区（等待发送的消息）
  final Map<String, dynamic> _sendingBuffer = {};

  // 消息去重缓存 - 临时使用，只在处理期间有效
  final Set<String> _processedMessages = <String>{};
  
  // 待处理消息跟踪器 - 这是允许保留的，用于跟踪等待AI回复的消息
  final Map<String, Map<String, dynamic>> _pendingMessages = {};
  
  // 当前正在加载消息的会话ID，用于防止切换角色时消息加载错误
  int currentLoadingConversationId = 0;
  
  // 最大待处理消息数
  static const int _maxPendingMessages = 20;
  
  // 定时清理Worker - 使用GetX
  Worker? _cleanupWorker;

  // 公开属性 - 提供访问内部状态的接口
  RxList<Message> get messages => _stateManager.messages;
  RxBool get isAiReplying => _stateManager.isAiReplying;
  RxBool get isLoadingHistory => _stateManager.isLoadingHistory;
  RxBool get isLoadingMore => _stateManager.isLoadingMore;
  RxBool get hasMoreMessages => _stateManager.hasMoreMessages;
  RxInt get currentPage => _stateManager.currentPage;
  String get currentconversationid => _stateManager.currentconversationid;
  
  // 添加角色回复状态访问
  RxMap<int, bool> get aiRoleReplyingMap => _stateManager.aiRoleReplyingMap;
  bool isRoleReplying(int roleId) => _stateManager.isAiRoleReplying(roleId);
  void setCurrentAiRoleId(int roleId) => _stateManager.setCurrentAiRoleId(roleId);
  
  // 配置参数
  final int pageSize;
  
  // 构造函数
  MessageService({
    required GlobalState globalState,
    required ChatRepository repository,
    required ChatManager chatManager,
    ISessionProvider? sessionProvider,
    int pageSize = 20,
    MessageStateManager? stateManager,
  }) : _globalState = globalState,
       _repository = repository,
       _chatManager = chatManager,
       _sessionProvider = sessionProvider,
       pageSize = pageSize,
       _stateManager = stateManager ?? MessageStateManager() {
    if (_sessionProvider == null) {
      LogUtil.warn('MessageService: 未提供ISessionProvider实现，会话列表将不会自动更新');
    }
  }
  
  @override
  void onInit() {
    super.onInit();
    // 设置消息处理事件监听器
    _setupMessageListeners();
    
    // 创建定期清理任务
    _setupCleanupTask();
    
    LogUtil.info('MessageService 已初始化');
  }
  
  /// 设置定期清理任务 - 使用Stream.periodic
  void _setupCleanupTask() {
    // 取消现有Worker
    _cleanupWorker?.dispose();

    // 使用Stream.periodic创建周期性清理任务
    final cleanupStream = Stream.periodic(
      const Duration(seconds: 30),
      (count) => count,
    );

    // 直接监听清理流
    cleanupStream.listen((_) => _cleanupCache());

    LogUtil.debug('已设置消息缓存定期清理Stream，间隔30秒');
  }
  
  /// 清理缓存
  void _cleanupCache() {
    try {
      // 清空消息去重缓存
      _processedMessages.clear();
      LogUtil.debug('已清空消息去重缓存');
      
      // 清理超时的待处理消息
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeoutThreshold = StringsConsts.pendingMessageExpiry; // 使用配置的过期时间
      
      final keysToRemove = <String>[];
      _pendingMessages.forEach((msgId, data) {
        final timestamp = data['timestamp'] as int? ?? 0;
        if (now - timestamp > timeoutThreshold) {
          keysToRemove.add(msgId);
        }
      });
      
      for (final key in keysToRemove) {
        _pendingMessages.remove(key);
      }
      
      if (keysToRemove.isNotEmpty) {
        LogUtil.debug('已清理${keysToRemove.length}个超时待处理消息');
      }
    } catch (e) {
      LogUtil.error('清理缓存失败: $e');
    }
  }
  
  /// 设置消息处理相关的事件监听器
  void _setupMessageListeners() {
    // 监听WebSocket连接状态变化
    _chatManager.isConnected.listen((connected) {
      if (connected) {
        LogUtil.debug('WebSocket已连接，可以发送消息');
      } else {
        LogUtil.debug('WebSocket已断开，无法发送消息');
      }
    });
    
    // 监听我们自己的AI回复状态
    this.isAiReplying.listen((replying) {
      LogUtil.debug('AI回复状态变化: replying=$replying');
    });
    
    // 记录日志
    LogUtil.debug('MessageService事件监听器已设置');
  }
  
  // 清除所有待处理消息
  void clearAllPendingMessages() {
    final count = _pendingMessages.length;
    _pendingMessages.clear();
    LogUtil.info('已清除所有待处理消息，共${count}条');
  }
  
  /// 清除所有消息缓存
  void clearAllMessageCache() {
    LogUtil.info('清除MessageService所有缓存');
    
    // 清除待处理消息
    clearAllPendingMessages();
    
    // 清除发送缓冲区
    _sendingBuffer.clear();
    
    // 清除消息去重缓存
    _processedMessages.clear();
    
    // 重置状态管理器
    _stateManager.reset();
    
    LogUtil.debug('MessageService缓存清理完成');
  }
  
  // 清除与特定角色相关的待处理消息
  void clearRoleRelatedPendingMessages(int roleId) {
    final messagesToRemove = <String>[];
    
    // 查找与特定角色相关的待处理消息
    _pendingMessages.forEach((messageId, data) {
      if (data['roleId'] == roleId) {
        messagesToRemove.add(messageId);
      }
    });
    
    // 移除找到的消息
    for (final messageId in messagesToRemove) {
      _pendingMessages.remove(messageId);
    }
    
    if (messagesToRemove.isNotEmpty) {
      LogUtil.info('已清除与角色ID=$roleId相关的${messagesToRemove.length}条待处理消息');
    }
  }
  
  // 移除与特定角色相关的待处理消息 - 私有方法，与clearRoleRelatedPendingMessages功能相同
  void _removeRolePendingMessages(int roleId) {
    clearRoleRelatedPendingMessages(roleId);
  }
  
  // 打印待处理消息日志
  void _logPendingMessages() {
    if (_pendingMessages.isEmpty) {
      return;
    }
    
    LogUtil.debug('===== 当前待处理消息列表(${_pendingMessages.length}条) =====');
    _pendingMessages.forEach((messageId, data) {
      final roleId = data['roleId'];
      final timestamp = data['timestamp'];
      final timeAgo = DateTime.now().millisecondsSinceEpoch - timestamp;
      LogUtil.debug('消息ID: $messageId, 角色ID: $roleId, 已等待: ${timeAgo}ms');
    });
    LogUtil.debug('=====================================');
  }
  
  // 添加待处理消息
  void _addPendingMessage(String messageId, int roleId) {
    _pendingMessages[messageId] = {
      'roleId': roleId,
      'timestamp': DateTime.now().millisecondsSinceEpoch
    };
    LogUtil.info('添加待处理消息: ID=$messageId, 角色ID=$roleId');
    
    // 打印待处理消息总数
    LogUtil.info('当前待处理消息总数: ${_pendingMessages.length}');
  }
  
  // 添加带完整消息内容的待处理消息
  void _addPendingMessageWithContent(Message message, int roleId) {
    // 获取消息的重要内容
    final messageContent = message.lastMessage;
    final senderUserId = message.senderUserId;
    final receiverUserId = message.receiverUserId;
    
    // 直接获取原始时间戳，保证不会受到时区影响
    final messageTimeMillis = message.time.millisecondsSinceEpoch;
    
    // 只保存必要的字段，不保存JSON，避免时区转换问题
    _pendingMessages[message.id] = {
      'roleId': roleId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'content': messageContent,
      'senderUserId': senderUserId,
      'receiverUserId': receiverUserId,
      'messageTime': messageTimeMillis, // 保存毫秒时间戳
      'status': message.status.toString(),
      'messageType': message.messageType.toString()
    };
    
    final originalTime = message.time;
    LogUtil.info('添加待处理消息: ID=${message.id}, 角色ID=$roleId, 原始时间=$originalTime, 时间戳=$messageTimeMillis, 内容: ${messageContent.length > 20 ? messageContent.substring(0, 20) + "..." : messageContent}');
    LogUtil.info('当前待处理消息总数: ${_pendingMessages.length}');
  }
  
  // 移除待处理消息
  void _removePendingMessage(String messageId) {
    final data = _pendingMessages[messageId];
    final roleId = data != null ? data['roleId'] as int? : null;
    _pendingMessages.remove(messageId);
    LogUtil.info('移除待处理消息: ID=$messageId, 角色ID=$roleId');
  }
  
  // 获取待处理消息的角色ID
  int? _getPendingMessageRoleId(String messageId) {
    final data = _pendingMessages[messageId];
    return data != null ? data['roleId'] as int : null;
  }
  
  // 检查消息是否为待处理消息
  bool _isPendingMessage(String messageId) {
    return _pendingMessages.containsKey(messageId);
  }
  
  /// 获取指定角色的所有待处理消息ID
  List<String> getPendingMessageIdsByRoleId(int roleId) {
    final pendingIds = <String>[];
    
    // 从全局待处理消息中查找与指定角色关联的消息
    _pendingMessages.forEach((messageId, data) {
      if (data['roleId'] == roleId) {
        pendingIds.add(messageId);
      }
    });
    
    return pendingIds;
  }
  
  /// 跟踪待处理消息（供外部调用）
  void trackPendingMessage(String messageId, int roleId) {
    _addPendingMessage(messageId, roleId);
  }
  
  /// 通过消息ID在待处理系统中获取完整消息（如果存在）
  Message? getPendingMessageById(String messageId) {
    try {
      final data = _pendingMessages[messageId];
      if (data == null) {
        return null;
      }
      
      // 如果包含必要的字段，直接构建消息对象
      if (data.containsKey('content') && data.containsKey('senderUserId') && data.containsKey('receiverUserId')) {
        final content = data['content'] as String;
        final senderUserId = data['senderUserId'] as String;
        final receiverUserId = data['receiverUserId'] as String;
        
        // 从原始时间戳恢复，避免时区转换
        final timestamp = data.containsKey('messageTime') ? data['messageTime'] as int : DateTime.now().millisecondsSinceEpoch;
        
        // 直接使用原始时间戳创建DateTime对象，不做任何时区转换
        final messageTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        
        // 获取其他字段（如果有）
        final messageTypeStr = data.containsKey('messageType') ? data['messageType'] as String? : null;
        final statusStr = data.containsKey('status') ? data['status'] as String? : null;
        
        // 解析消息类型和状态
        MessageType messageType = MessageType.text;
        try {
          if (messageTypeStr != null && messageTypeStr.isNotEmpty) {
            messageType = messageTypeStr.contains("MessageType.") ? 
              messageTypeStr.split('.').last.toEnum() : 
              messageTypeStr.toEnum();
          }
        } catch (e) {
          LogUtil.warn('解析消息类型失败: $messageTypeStr, $e');
        }
        
        MessageStatus status = MessageStatus.sending;
        try {
          if (statusStr != null && statusStr.isNotEmpty) {
            switch (statusStr.toLowerCase()) {
              case 'sending':
                status = MessageStatus.sending;
                break;
              case 'sent':
                status = MessageStatus.sent;
                break;
              case 'delivered':
                status = MessageStatus.delivered;
                break;
              case 'read':
                status = MessageStatus.read;
                break;
              case 'failed':
                status = MessageStatus.failed;
                break;
            }
          }
        } catch (e) {
          LogUtil.warn('解析消息状态失败: $statusStr, $e');
        }
        
        // 创建消息对象
        Message message = Message(
          id: messageId,
          lastMessage: content,
          senderUserId: senderUserId, 
          receiverUserId: receiverUserId,
          messageType: messageType,
          status: status,
          time: messageTime,
        );
        
        LogUtil.info('从原始字段恢复待处理消息: ID=$messageId, 时间戳=$timestamp, 时间=${message.time}');
        return message;
      }
      
      LogUtil.warn('待处理消息缺少必要字段: $messageId');
      return null;
    } catch (e) {
      LogUtil.error('获取待处理消息失败: $e');
      return null;
    }
  }
  
  /// 获取指定角色的所有待处理消息
  List<Message> getPendingMessagesByRoleId(int roleId) {
    final List<Message> messages = [];
    final pendingIds = getPendingMessageIdsByRoleId(roleId);
    
    for (final id in pendingIds) {
      final msg = getPendingMessageById(id);
      if (msg != null) {
        messages.add(msg);
        LogUtil.info('恢复角色$roleId的待处理消息: ID=${msg.id}, 内容=${msg.lastMessage.substring(0, math.min(20, msg.lastMessage.length))}');
      }
    }
    
    return messages;
  }
  
  /// 发送消息
  Future<bool> sendMessage(String content, {int? conversationId, String? conversationid}) async {
    // 验证消息
    final validationResult = _repository.validateMessage(content);
    if (validationResult['isValid'] == false) {
      final errorMessage = validationResult['errorMessage'] as String;
      // 直接使用 handleException 处理业务异常
      ErrorHandler.handleException(
        AppException(
          errorMessage,
          code: ErrorCodes.BUSINESS_ERROR
        )
      );
      return false;
    }
    
    try {
      // 获取角色ID
      final aiRoleId = _chatManager.activeAiRoleId.value;
      
      // 获取当前会话ID - 这里是关键修改点
      // 如果明确提供了会话ID，使用提供的值
      // 否则使用ChatManager中的activeConversationId
      final activeConversationId = conversationId ?? _chatManager.activeConversationId.value;
      
      // 获取会话ID和用户ID
      final activeconversationid = conversationid ?? _stateManager.currentconversationid;
      final userId = _globalState.currentUser.value?.uid ?? 'user';
      
      // 创建用户消息
      final message = _repository.createUserMessage(
        content, 
        userId,
        'ai_$aiRoleId',
        conversationId: activeConversationId,
      );
      
      // 添加消息到列表
      await _addMessage(message);
      
      // 设置AI正在回复状态
      isAiReplying.value = true;
      
      // 准备发送数据
      Map<String, dynamic> sendData = {
        'content': content,
        'role_id': aiRoleId, // 始终添加role_id，无论是否为新会话
      };
      
      // 是否为首次发送（创建新会话）
      final isNewConversation = activeConversationId <= 0;
      
      // 根据是否为新会话设置conversation_id
      if (isNewConversation) {
        LogUtil.info('检测到新会话，添加role_id: $aiRoleId');
        // 新会话的conversation_id为null，让服务器创建新会话
        sendData['conversation_id'] = null;
      } else {
        // 已有会话，使用现有的conversation_id
        sendData['conversation_id'] = activeConversationId;
        LogUtil.info('发送到现有会话，conversation_id: $activeConversationId, role_id: $aiRoleId');
      }
      
      // 添加到待处理消息跟踪系统，包含完整消息内容
      _addPendingMessageWithContent(message, aiRoleId);
      
      // 发送消息到WebSocket
      final sendResult = _wsManager.sendToChannel(
        event: WsEvent.chat_message,
        data: sendData,
        channelId: isNewConversation ? null : activeConversationId, // 第一次聊天时使用null，后续使用activeConversationId
        messageId: message.id, // 传递消息ID，用于跟踪
      );
      
      if (!sendResult) {
        await _handleSendFailure(message.id);
        
        // 如果是首次发送失败，还需要清理待创建的会话状态
        if (isNewConversation) {
          _handleNewConversationFailure();
        }
        
        // 从待处理消息中移除
        _removePendingMessage(message.id);
        
        return false;
      }
      
      // 更新消息状态为已发送
      await updateMessageStatus(message.id, MessageStatus.sent);
      
      // 通知会话更新
      if (activeConversationId > 0) {
        _notifySessionUpdated(activeConversationId);
      } else {
        // 即使是新会话也立即更新
        updateSessionsList();
      }
      
      return true;
    } catch (e) {
      LogUtil.error('发送消息失败: $e');
      
      // 使用 ErrorHandler 统一处理异常
      ErrorHandler.handleException(
        AppException(
          'failed to send message', 
          code: ErrorCodes.CHAT_SEND_FAILED,
          originalError: e
        ),
        showSnackbar: false, // 不显示Snackbar，防止UI重复提示
      );
      
      isAiReplying.value = false;
      return false;
    }
  }
  
  /// 处理发送失败
  Future<void> _handleSendFailure(String messageId) async {
    LogUtil.warn('WebSocket发送消息失败，可能是连接已断开');
    await updateMessageStatus(messageId, MessageStatus.failed);
    isAiReplying.value = false;
  }
  
  /// 处理新会话创建失败
  void _handleNewConversationFailure() {
    LogUtil.warn('新会话创建失败，回滚状态');
    

    
    // 结束AI回复状态
    isAiReplying.value = false;
  }
  
  /// 添加消息到列表
  Future<void> _addMessage(Message message) async {
    await _messagesLock.synchronized(() {
      if (!_stateManager.processedMessageIds.contains(message.id)) {
        _stateManager.messages.add(message);
        _stateManager.processedMessageIds.add(message.id);
        _stateManager.messages.refresh();
        LogUtil.debug('消息已添加到列表: ${message.id}');
        
        // 立即更新会话列表
        if (message.conversationId != null && message.conversationId! > 0) {
          _notifySessionUpdated(message.conversationId!);
        } else {
          // 如果没有conversationId，使用当前活跃会话ID
          _notifySessionUpdated(_chatManager.activeConversationId.value);
        }
      }
    });
  }
  
  /// 更新消息状态
  Future<void> updateMessageStatus(String messageId, MessageStatus status) async {
    final messageIndex = _stateManager.messages.indexWhere((m) => m.id == messageId);
    if (messageIndex != -1) {
      final message = _stateManager.messages[messageIndex];
      _stateManager.messages[messageIndex] = _stateManager.messages[messageIndex].copyWith(status: status);
      _stateManager.messages.refresh();
      LogUtil.debug('消息状态已更新: ID=$messageId, 新状态=$status');
      
      // 立即更新会话列表
      if (message.conversationId != null && message.conversationId! > 0) {
        _notifySessionUpdated(message.conversationId!);
      } else {
        // 如果没有conversationId，使用当前活跃会话ID
        _notifySessionUpdated(_chatManager.activeConversationId.value);
      }
    } else {
      LogUtil.warn('无法更新消息状态，消息ID不存在: $messageId');
    }
  }
  
  /// 重置状态
  void reset() {
    _stateManager.reset();
  }
  
  /// 切换会话ID
  void switchSession(String newconversationid) {
    _stateManager.switchSession(newconversationid);
  }
  
  /// 生成新的会话ID
  String generateNewconversationid() {
    return _stateManager.generateNewconversationid();
  }
  
  /// 清除特定会话的状态
  void clearConversationState(int conversationId) {
    _stateManager.clearConversationState(conversationId);
  }
  
  /// 处理历史消息
  void processHistoryMessages(List<Message> historyMessages, int conversationId, bool resetPage) {
    try {
      // 获取当前会话的分页状态
      final conversationState = _stateManager.getConversationState(conversationId);
      
      // 如果是重置页码，则清空现有消息列表并重置会话状态
      if (resetPage) {
        _stateManager.messages.clear();
        conversationState.reset();
      }
      
      // 同步状态
      _stateManager.currentPage.value = conversationState.currentPage;
      _stateManager.hasMoreMessages.value = conversationState.hasMoreMessages;
      
      if (historyMessages.isNotEmpty) {
        _validateMessagesForCurrentRole(historyMessages);
        
        // 添加消息到列表
        if (resetPage) {
          // 第一页显示正序消息（与后端返回顺序相反）
          // 后端返回的消息是最新的在前面，我们需要反转顺序来显示
          for (int i = 0; i < historyMessages.length; i++) {
            final message = historyMessages[i];
            _stateManager.messages.add(message);
            _stateManager.processedMessageIds.add(message.id);
          }
        } else {
          // 分页加载时（第二页及以后），按原顺序插入历史消息到列表开头
          for (int i = historyMessages.length - 1; i >= 0; i--) {
            final message = historyMessages[i];
            _stateManager.messages.insert(0, message);
            _stateManager.processedMessageIds.add(message.id);
          }
        }
        
        // 更新分页状态
        conversationState.currentPage++;
        _stateManager.currentPage.value = conversationState.currentPage;
        
        // 判断是否还有更多消息
        _updateHasMoreMessages(historyMessages.length, conversationState);
        
        // 如果没有更多历史消息且是首次加载，在顶部添加角色简介
        if (!conversationState.hasMoreMessages && resetPage) {
          _addRoleIntroAtTop();
        }
      } else {
        _handleNoHistoryMessages(conversationState, resetPage);
      }
      
      // 去重
      _deduplicateMessages();
      
      LogUtil.info('处理历史消息完成，共${_stateManager.messages.length}条消息');
    } catch (e) {
      LogUtil.error('处理历史消息失败: $e');
      
      // 使用 ErrorHandler 统一处理异常
      ErrorHandler.handleException(
        AppException(
          'failed to process history messages',
          code: ErrorCodes.CHAT_HISTORY_LOAD_FAILED,
          originalError: e
        ),
        showSnackbar: false, // 不显示Snackbar，因为这是内部处理错误
      );
    }
  }
  
  /// 更新是否有更多消息状态
  void _updateHasMoreMessages(int messageCount, ConversationPagingState state) {
    final hasMore = messageCount >= pageSize;
    state.hasMoreMessages = hasMore;
    _stateManager.hasMoreMessages.value = hasMore;
    
    if (!hasMore) {
      LogUtil.debug('没有更多历史消息');
    }
  }
  
  /// 处理无历史消息情况
  void _handleNoHistoryMessages(ConversationPagingState state, bool resetPage) {
    LogUtil.debug('没有找到历史消息或消息列表为空');
    state.hasMoreMessages = false;
    _stateManager.hasMoreMessages.value = false;
    
    // 如果重置页码且没有消息，添加角色简介
    if (resetPage) {
      _addRoleIntroAtTop();
    }
  }
  
  /// 在消息列表顶部添加角色简介
  Future<void> _addRoleIntroAtTop() async {
    try {
      // 获取当前角色信息
      final aiRoleId = _chatManager.activeAiRoleId.value;
      final currentRole = _chatManager.currentRole.value;
      
      if (currentRole != null && currentRole.description.isNotEmpty) {
        // 添加角色简介系统消息
        await addRoleIntroMessage(currentRole.description, forceAdd: true);
        LogUtil.info('已在消息列表顶部添加角色简介: ${currentRole.name}');
      } else {
        // 如果没有角色描述，添加默认简介
        await addRoleIntroMessage('Intro: A rebellious teenager who loves rock music and often skips school. You are her tutor who tries to help her get back on track.', forceAdd: true);
        LogUtil.info('已在消息列表顶部添加默认角色简介');
      }
    } catch (e) {
      LogUtil.error('添加角色简介失败: $e');
    }
  }
  
  /// 验证消息是否属于当前角色
  void _validateMessagesForCurrentRole(List<Message> historyMessages) {
    final aiRoleId = _chatManager.activeAiRoleId.value;
    final expectedAiSender = 'ai_$aiRoleId';
    
    final hasIncorrectMessages = historyMessages.any((msg) => 
      msg.senderUserId.startsWith('ai_') && msg.senderUserId != expectedAiSender);
    
    if (hasIncorrectMessages) {
      LogUtil.warn('发现不属于当前角色($expectedAiSender)的消息，可能是历史消息混淆，将继续显示');
    }
  }
  
  /// 消息去重
  void _deduplicateMessages() {
    if (_stateManager.messages.isEmpty || _stateManager.messages.length == 1) return;
    
    final uniqueMessages = <Message>[];
    final uniqueIds = <String>{};
    
    for (final msg in _stateManager.messages) {
      if (!uniqueIds.contains(msg.id)) {
        uniqueMessages.add(msg);
        uniqueIds.add(msg.id);
      } else {
        LogUtil.debug('发现重复消息，已跳过: ID=${msg.id}');
      }
    }
    
    // 如果找到了重复消息，更新消息列表
    if (uniqueMessages.length < _stateManager.messages.length) {
      LogUtil.info('已从消息列表中移除${_stateManager.messages.length - uniqueMessages.length}条重复消息');
      _stateManager.messages.clear();
      _stateManager.messages.addAll(uniqueMessages);
      _stateManager.messages.refresh();
    }
  }
  
  /// 添加系统消息
  Future<void> addSystemMessage(String content) async {
    try {
      // 获取用户ID
      final userId = _globalState.currentUser.value?.uid ?? 'user';
      
      // 创建系统消息
      final message = _repository.createSystemMessage(
        content,
        userId,
        conversationId: _chatManager.activeConversationId.value,
      );
      
      // 添加消息到列表
      await _addMessage(message);
      
      LogUtil.debug('已添加系统消息: $content');
    } catch (e) {
      LogUtil.error('添加系统消息失败: $e');
    }
  }
  
  /// 添加角色简介系统消息
  /// 
  /// 在消息列表顶部添加角色简介系统消息，仅当消息列表为空或需要强制添加时执行
  /// [intro] 角色简介内容
  /// [forceAdd] 是否强制添加，即使消息列表不为空
  /// [roleId] 角色ID，默认使用当前活跃角色
  Future<void> addRoleIntroMessage(String intro, {bool forceAdd = false, int? roleId}) async {
    try {
      // 获取角色ID，如果未提供则使用当前活跃角色ID
      final currentRoleId = roleId ?? _chatManager.activeAiRoleId.value;
      
      // 检查是否需要添加简介消息
      final needToAddIntro = forceAdd || _stateManager.messages.isEmpty;
      
      if (needToAddIntro) {
        // 获取用户ID
        final userId = _globalState.currentUser.value?.uid ?? 'user';
        
        // 创建系统消息
        final message = _repository.createSystemMessage(
          intro,
          userId,
          conversationId: _chatManager.activeConversationId.value,
        );
        
        // 在消息列表顶部插入
        await _messagesLock.synchronized(() {
          if (!_stateManager.processedMessageIds.contains(message.id)) {
            // 在列表开头插入消息
            _stateManager.messages.insert(0, message);
            _stateManager.processedMessageIds.add(message.id);
            _stateManager.messages.refresh();
            LogUtil.debug('已在消息列表顶部添加角色简介: ${message.id}, 角色ID=$currentRoleId');
          }
        });
        
        LogUtil.info('已添加角色简介系统消息: 角色ID=$currentRoleId, 内容="${intro.length > 30 ? intro.substring(0, 30) + "..." : intro}"');
      } else {
        LogUtil.debug('消息列表不为空且未强制添加，跳过添加角色简介');
      }
    } catch (e) {
      LogUtil.error('添加角色简介系统消息失败: $e');
    }
  }
  
  /// 处理AI回复开始
  void handleAiReplyStart() {
    // 获取当前角色ID
    final currentRoleId = _chatManager.activeAiRoleId.value;
    
    // 更新状态管理器中的状态
    _stateManager.setCurrentAiRoleId(currentRoleId);
    _stateManager.updateAiReplyingStatus(true);
    
    // 触发事件 - 暂时注释，可能需要在GlobalEventState中添加相应事件
    // GlobalEventState.to.triggerAiReplyStarted(roleId: currentRoleId);
    
    LogUtil.debug('AI回复开始: roleId=$currentRoleId');
  }
  
  /// 处理AI回复结束
  void handleAiReplyEnd() {
    // 获取当前角色ID
    final currentRoleId = _chatManager.activeAiRoleId.value;
    
    // 更新状态管理器中的状态
    _stateManager.updateAiReplyingStatus(false);
    
    LogUtil.debug('AI回复结束: roleId=$currentRoleId');
  }
  
  /// 处理WebSocket消息（统一入口）
  void processWebSocketMessage(int roleId, WsMsg wsMsg) {
    try {
      LogUtil.debug('MessageService收到WebSocket消息: roleId=$roleId, event=${wsMsg.event}');

      // 确保数据中包含事件类型
      final messageData = Map<String, dynamic>.from(wsMsg.data);
      messageData['event'] = wsMsg.event.toString();

      // 提取会话ID
      final conversationId = wsMsg.channelId;

      // 获取当前活跃角色ID
      final currentAiRoleId = _chatManager.activeAiRoleId.value;
      final isActiveRole = (roleId == currentAiRoleId);

      LogUtil.debug('处理消息: roleId=$roleId, isActiveRole=$isActiveRole, conversationId=$conversationId');

      if (isActiveRole) {
        // 活跃角色：正常处理并显示在UI
        processAiMessage(messageData);
      } else {
        // 非活跃角色：只绑定会话ID和存储待处理消息
        _processNonActiveRoleMessage(roleId, wsMsg, conversationId);
      }
    } catch (e) {
      LogUtil.error('处理WebSocket消息失败: $e');
    }
  }

  /// 处理非活跃角色消息
  void _processNonActiveRoleMessage(int roleId, WsMsg wsMsg, int? conversationId) {
    try {
      LogUtil.debug('处理非活跃角色消息: roleId=$roleId, conversationId=$conversationId');

      // 1. 绑定会话ID（如果有）
      if (conversationId != null && conversationId > 0) {
        // 调用ChatManager处理会话信息绑定
        try {
          _chatManager.handleWebSocketSessionInfo({
            'conversation_id': conversationId.toString(),
            'role_id': roleId.toString()
          });
          LogUtil.debug('已绑定非活跃角色会话: roleId=$roleId, conversationId=$conversationId');
        } catch (e) {
          LogUtil.error('绑定非活跃角色会话失败: $e');
        }
      }

      // 2. 更新角色状态
      _stateManager.setAiRoleReplyingStatus(roleId, false);

      // 3. 移除该角色相关的待处理消息（如果是AI回复）
      _removeRolePendingMessages(roleId);
      LogUtil.debug('AI已回复，移除角色$roleId的待处理消息');
      

      // 4. 触发会话更新（如果有会话ID）
      if (conversationId != null && conversationId > 0) {
        _notifySessionUpdated(conversationId);
      }

    } catch (e) {
      LogUtil.error('处理非活跃角色消息失败: $e');
    }
  }





  /// 处理接收到的AI消息
  void processAiMessage(Map<String, dynamic> data) {
    try {
     
      // 获取当前AI角色ID和会话ID
      final currentAiRoleId = _chatManager.activeAiRoleId.value;
      final currentConversationId = _chatManager.activeConversationId.value;
      
      // 使用仓库方法提取和处理消息数据
      final extractedData = _repository.extractAiMessageData(data, currentAiRoleId);
      final messageData = extractedData['messageData'];
      final receivedRoleId = extractedData['receivedRoleId'] as int?;
      final receivedConversationId = extractedData['receivedConversationId'] as int?;
      final replyToMessageId = extractedData['replyToMessageId'] as String?;
      
      // 如果没有有效的消息数据，则退出处理
      if (messageData == null) {
        LogUtil.warn('无法提取有效的AI消息数据');
        handleAiReplyEnd();
        return;
      }
      
      // 打印所有待处理消息，帮助调试
      LogUtil.info('处理AI回复时，当前待处理消息总数: ${_pendingMessages.length}');
      if (_pendingMessages.isNotEmpty) {
        _pendingMessages.forEach((id, data) {
          LogUtil.info('待处理消息: ID=$id, 角色ID=${data['roleId']}, 等待时间=${DateTime.now().millisecondsSinceEpoch - (data['timestamp'] as int)}ms');
        });
      }
      
      // 检查是否是待处理消息的响应
      bool isPendingMessageResponse = false;
      int? pendingMessageRoleId;
      
      if (replyToMessageId != null && _isPendingMessage(replyToMessageId)) {
        isPendingMessageResponse = true;
        pendingMessageRoleId = _getPendingMessageRoleId(replyToMessageId);
        
        // 如果是待处理消息的响应，使用待处理消息的角色ID
        if (pendingMessageRoleId != null) {
          LogUtil.debug('检测到待处理消息响应: messageId=$replyToMessageId, roleId=$pendingMessageRoleId');
        }
        
        // 从待处理消息中移除
        _removePendingMessage(replyToMessageId);
      } else {
        // 如果没有明确的回复消息ID，查找可能对应的待处理消息
        // 检查角色ID和会话ID是否匹配
        if (receivedRoleId != null && receivedConversationId != null) {
          // 查找与当前角色ID相关的所有待处理消息
          List<String> potentialPendingMessageIds = [];
          _pendingMessages.forEach((msgId, data) {
            if (data['roleId'] == receivedRoleId) {
              potentialPendingMessageIds.add(msgId);
            }
          });
          
          // 如果有待处理消息，选择最早的一个进行处理
          if (potentialPendingMessageIds.isNotEmpty) {
            // 按时间戳排序，处理最早的一个
            potentialPendingMessageIds.sort((a, b) {
              final timeA = _pendingMessages[a]?['timestamp'] as int? ?? 0;
              final timeB = _pendingMessages[b]?['timestamp'] as int? ?? 0;
              return timeA.compareTo(timeB);
            });
            
            final oldestPendingId = potentialPendingMessageIds.first;
            isPendingMessageResponse = true;
            pendingMessageRoleId = _getPendingMessageRoleId(oldestPendingId);
            
            LogUtil.debug('找到可能的待处理消息: messageId=$oldestPendingId, roleId=$pendingMessageRoleId');
            
            // 从待处理消息中移除
            _removePendingMessage(oldestPendingId);
          }
        }
      }
      
      // 使用仓库方法处理会话信息
      final sessionInfo = _repository.processSessionInfo(
        currentConversationId,
        currentAiRoleId,
        receivedConversationId,
        receivedRoleId,
        pendingMessageRoleId
      );
      
      final bool isNewSessionInfo = sessionInfo['isNewSessionInfo'] as bool;
      final int useConversationId = sessionInfo['useConversationId'] as int;
      final int useRoleId = sessionInfo['useRoleId'] as int;
      
      // 检查消息是否属于当前活跃角色
      final bool isForActiveRole = (useRoleId == currentAiRoleId);
      
      // 处理会话信息更新 - 无论是否为当前活跃角色都需要处理
      if (isNewSessionInfo) {
        LogUtil.debug('处理会话信息: conversation_id=$receivedConversationId, role_id=$receivedRoleId');
        
        // 关键修复：调用ChatManager处理会话信息（这是之前缺失的步骤）
        try {
          _chatManager.handleWebSocketSessionInfo({
            'conversation_id': receivedConversationId?.toString(),
            'role_id': receivedRoleId?.toString()
          });
          LogUtil.debug('已成功调用ChatManager.handleWebSocketSessionInfo处理会话绑定');
        } catch (e) {
          LogUtil.error('调用ChatManager.handleWebSocketSessionInfo失败: $e');
          // 继续处理，不中断消息流程
        }
      
        
        // 移除conversation ID处理器相关代码，根据用户要求不需要这些处理器
        LogUtil.debug('跳过会话绑定更新通知，用户不需要conversation ID处理器');
      } else {
        LogUtil.debug('无新会话信息需要处理');
      }
      
      if (!isForActiveRole) {
        LogUtil.info('收到非当前活跃角色的消息: 当前角色ID=${currentAiRoleId}, 消息角色ID=${useRoleId}');
        
        // 即使不是当前活跃角色的消息，也需要尝试解析并存储，而不是直接退出
        try {
          // 使用仓库解析消息
          final message = _repository.parseRealTimeMessage(messageData, useRoleId);
          if (message != null && receivedConversationId != null && receivedConversationId > 0) {
            // 只记录日志，不添加到当前UI
            LogUtil.debug('为非活跃角色(${useRoleId})存储会话ID绑定: conversationId=${receivedConversationId}');
            
            // 移除conversation ID处理器相关代码，根据用户要求不需要这些处理器
            LogUtil.debug('跳过非活跃角色会话绑定通知，用户不需要conversation ID处理器');
            
            // 更新非活跃角色的状态
            _stateManager.setAiRoleReplyingStatus(useRoleId, false);
            
            // 触发针对该角色的回复结束事件，而不是全局事件 - 暂时注释
            // GlobalEventState.to.triggerAiReplyFinished(roleId: useRoleId);
            
            // 移除该角色相关的待处理消息
            _removeRolePendingMessages(useRoleId);
            
            LogUtil.info('已处理非活跃角色(${useRoleId})的消息并重置其状态，不影响当前活跃角色(${currentAiRoleId})的状态');
          }
        } catch (e) {
          LogUtil.error('处理非活跃角色消息失败: $e');
        }
        
        // 不影响当前活跃角色的状态，直接返回
        return;
      }
      
      // 使用仓库解析消息
      final message = _repository.parseRealTimeMessage(messageData, useRoleId);
      if (message == null) {
        LogUtil.warn('无法创建AI消息对象');
        handleAiReplyEnd();
        return;
      }
      
      // 添加会话信息
      final messageWithSession = message.copyWith(
        conversationId: useConversationId > 0 ? useConversationId : message.conversationId
      );
      
      // 获取当前用户ID
      final currentUserId = _globalState.currentUser.value?.uid ?? 'user';
      
      // 验证并纠正消息发送者
      final validatedMessage = _repository.validateMessageSender(messageWithSession, useRoleId, currentUserId);
      
      // 添加消息到列表（_addMessage内部已经会调用_notifySessionUpdated，这里不需要重复调用）
      _addMessage(validatedMessage);
      
      // AI回复结束
      handleAiReplyEnd();
      
      // 记录当前待处理消息状态
      LogUtil.debug('处理AI消息后，当前待处理消息数量: ${_pendingMessages.length}');
    } catch (e) {
      LogUtil.error('处理AI消息失败: $e');
      handleAiReplyEnd();
    }
  }
  

  
  /// 发送正在输入状态
  Future<void> sendTypingStatus(bool isTyping) async {
    // 获取会话ID
    final activeConversationId = _chatManager.activeConversationId.value;
    if (activeConversationId <= 0) {
      return;
    }
    
    // 发送输入状态
    _wsManager.sendToChannel(
      event: WsEvent.typing,
      data: {
        'is_typing': isTyping,
        'conversation_id': activeConversationId
      },
    );
  }
  
  /// 添加AI问候消息
  Future<void> addAiGreetingMessage(String greeting, int aiRoleId) async {
    try {
      // 获取用户ID
      final userId = _globalState.currentUser.value?.uid ?? 'user';
      
      // 创建AI问候消息
      final message = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        lastMessage: greeting,
        senderUserId: 'ai_$aiRoleId',
        receiverUserId: userId,
        messageType: MessageType.text,
        status: MessageStatus.sent,
        time: DateTime.now(),
      );
      
      // 添加消息到列表
      await _addMessage(message);
      
      LogUtil.debug('已添加AI问候消息: $greeting');
    } catch (e) {
      LogUtil.error('添加AI问候消息失败: $e');
    }
  }
  
  /// 通知会话更新
  void _notifySessionUpdated(int conversationId) {
    try {
      if (conversationId <= 0) {
        LogUtil.debug('无有效会话ID，跳过更新');
        return;
      }
      
      // 获取当前会话的最新消息
      final currentSessionMessages = _stateManager.messages
          .where((m) => m.conversationId == conversationId)
          .toList();
      
      String? latestMessage;
      DateTime? latestMessageTime;
      
      if (currentSessionMessages.isNotEmpty) {
        // 按时间降序排序，获取最新消息
        // 找到最新消息（不使用排序，而是遍历找到时间最晚的消息）
        Message newestMessage = currentSessionMessages.first;
        for (final message in currentSessionMessages) {
          if (message.time.isAfter(newestMessage.time)) {
            newestMessage = message;
          }
        }
        latestMessage = newestMessage.lastMessage;
        latestMessageTime = newestMessage.time;
      }
      
      // 直接更新会话列表，不再使用事件广播
      if (_sessionProvider != null) {
        try {
          _sessionProvider.refreshSessions(refresh: true);
          LogUtil.debug('已直接触发会话列表更新: conversationId=$conversationId');
        } catch (e) {
          LogUtil.error('触发会话列表更新失败: $e');
        }
      }
    } catch (e) {
      LogUtil.error('通知会话更新失败: $e');
    }
  }
  
  /// 更新会话列表
  void updateSessionsList() {
    final conversationId = _chatManager.activeConversationId.value;
    _notifySessionUpdated(conversationId);
  }
}